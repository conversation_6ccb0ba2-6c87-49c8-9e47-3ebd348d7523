"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, isCollapsed = false, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance, showCloseButton = true, onDownloadModal, onAttachmentsModal, onStatisticsModal } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animatingChat, setAnimatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configDropdownOpen, setConfigDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Função para mover chat para o topo com animação\n    const moveToTop = async (chatId)=>{\n        try {\n            // Animar o chat\n            setAnimatingChat(chatId);\n            // Atualizar o timestamp no Firestore para mover para o topo\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chatId), {\n                lastUpdatedAt: new Date().toISOString()\n            });\n            // Recarregar chats após um pequeno delay para mostrar a animação\n            setTimeout(()=>{\n                loadChats();\n                setAnimatingChat(null);\n            }, 300);\n        } catch (error) {\n            console.error(\"Erro ao mover chat para o topo:\", error);\n            setAnimatingChat(null);\n        }\n    };\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance,\n            moveToTop: moveToTop\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    // Função para deletar todos os anexos de um chat\n    const deleteChatAttachments = async (chatId)=>{\n        try {\n            const attachmentsRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(chatId, \"/anexos\"));\n            const attachmentsList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.listAll)(attachmentsRef);\n            // Deletar todos os arquivos de anexos\n            const deletePromises = attachmentsList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(item));\n            await Promise.all(deletePromises);\n            console.log(\"\".concat(attachmentsList.items.length, \" anexos deletados do chat \").concat(chatId));\n        } catch (error) {\n            console.log(\"Erro ao deletar anexos ou pasta de anexos n\\xe3o encontrada:\", error);\n        }\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Deletar arquivo chat.json do Storage\n                try {\n                    const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(chatJsonRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo chat.json no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Deletar todos os anexos do chat\n                await deleteChatAttachments(deleteConfirmModal.id);\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"\\n        \").concat(isCollapsed ? \"lg:-translate-x-full\" : \"lg:translate-x-0\", \"\\n      \"),\n                children: [\n                    showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onToggle,\n                        className: \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-blue-600 hover:bg-blue-500 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-xl border-2 border-white/20 z-10 group\",\n                        title: \"Fechar sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white transition-transform duration-300 group-hover:rotate-180\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 673,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold text-lg\",\n                                                    children: userData.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Carregando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-400 text-sm\",\n                                                    title: openRouterBalance.error,\n                                                    children: \"$0.00\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-200 text-sm\",\n                                                    children: [\n                                                        \"$\",\n                                                        openRouterBalance.balance.toFixed(4)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onSettingsOpen,\n                                            className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-2 border-b border-white/5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleHomeClick,\n                                        className: \"group relative w-full bg-gradient-to-r from-blue-600/90 to-cyan-600/90 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-300 overflow-hidden shadow-md shadow-blue-900/15 hover:shadow-lg hover:shadow-blue-800/25 border border-blue-400/20 hover:border-cyan-400/40 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center py-2.5 px-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-lg bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 737,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-white/95 group-hover:text-white transition-colors duration-300\",\n                                                                children: \"\\xc1rea Inicial\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-1 group-hover:translate-x-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/80\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2.5,\n                                                                d: \"M9 5l7 7-7 7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-1.5 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleNewChat,\n                                                className: \"group relative bg-gradient-to-br from-blue-600/80 to-cyan-600/80 hover:from-blue-500/90 hover:to-cyan-500/90 text-white rounded-lg transition-all duration-300 overflow-hidden shadow-sm shadow-blue-900/10 hover:shadow-md hover:shadow-blue-800/20 border border-blue-400/15 hover:border-cyan-400/30 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex flex-col items-center justify-center py-2.5 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-md bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300 mb-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M12 4v16m8-8H4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-white/90 group-hover:text-white transition-colors duration-300 text-center leading-tight\",\n                                                                children: [\n                                                                    \"Nova\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Conversa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleNewFolder,\n                                                className: \"group relative bg-gradient-to-br from-blue-600/80 to-cyan-600/80 hover:from-blue-500/90 hover:to-cyan-500/90 text-white rounded-lg transition-all duration-300 overflow-hidden shadow-sm shadow-blue-900/10 hover:shadow-md hover:shadow-blue-800/20 border border-blue-400/15 hover:border-cyan-400/30 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                                title: \"Criar Nova Pasta\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex flex-col items-center justify-center py-2.5 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-md bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300 mb-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 799,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-white/90 group-hover:text-white transition-colors duration-300 text-center leading-tight\",\n                                                                children: [\n                                                                    \"Nova\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Pasta\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Conversas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-2 py-2\",\n                                        children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                onDragOver: (e)=>handleDragOver(e, folder.id),\n                                                onDragLeave: handleDragLeave,\n                                                onDrop: (e)=>handleDrop(e, folder.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                        onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                        onMouseLeave: ()=>setHoveredFolder(null),\n                                                        onClick: ()=>toggleFolder(folder.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M9 5l7 7-7 7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 854,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                            style: {\n                                                                                backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                style: {\n                                                                                    color: getFolderHexColor(folder.color)\n                                                                                },\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 869,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 863,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                            children: folder.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 876,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                            children: folder.chats.length\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 879,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                    children: folder.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 884,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                            title: \"Editar pasta\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleEditFolder(folder.id);\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 905,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                            title: \"Deletar pasta\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleDeleteFolder(folder.id, folder.name);\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 917,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 908,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 842,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-6 mt-2 space-y-1\",\n                                                        children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                                chat: chat,\n                                                                isActive: currentChat === chat.id,\n                                                                onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                                onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                                onDragStart: handleDragStart,\n                                                                onDragEnd: handleDragEnd,\n                                                                isDragging: draggedChat === chat.id,\n                                                                isAnimating: animatingChat === chat.id,\n                                                                configDropdownOpen: configDropdownOpen,\n                                                                setConfigDropdownOpen: setConfigDropdownOpen,\n                                                                onDownloadModal: onDownloadModal,\n                                                                onAttachmentsModal: onAttachmentsModal,\n                                                                onStatisticsModal: onStatisticsModal\n                                                            }, chat.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 928,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, folder.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-2 py-2\",\n                                        onDragOver: (e)=>handleDragOver(e, null),\n                                        onDragLeave: handleDragLeave,\n                                        onDrop: (e)=>handleDrop(e, null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Sem Pasta \",\n                                                                dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                                children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-white/30\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/40 text-xs\",\n                                                            children: \"Nenhuma conversa sem pasta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 19\n                                                }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                        chat: chat,\n                                                        isActive: currentChat === chat.id,\n                                                        onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                        onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                        onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                        onDragStart: handleDragStart,\n                                                        onDragEnd: handleDragEnd,\n                                                        isDragging: draggedChat === chat.id,\n                                                        isAnimating: animatingChat === chat.id,\n                                                        configDropdownOpen: configDropdownOpen,\n                                                        setConfigDropdownOpen: setConfigDropdownOpen,\n                                                        onDownloadModal: onDownloadModal,\n                                                        onAttachmentsModal: onAttachmentsModal,\n                                                        onStatisticsModal: onStatisticsModal\n                                                    }, chat.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 967,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/30 mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-12 h-12 mx-auto\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1008,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/50 text-sm\",\n                                                children: \"Nenhuma conversa ainda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/30 text-xs mt-1\",\n                                                children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1029,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 650,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1036,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1045,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1058,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1066,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1078,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1093,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"PnQLWBuot+6j6fSnAuBQlETMF5k=\")), \"PnQLWBuot+6j6fSnAuBQlETMF5k=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging, isAnimating = false, configDropdownOpen, setConfigDropdownOpen, onDownloadModal, onAttachmentsModal, onStatisticsModal } = param;\n    _s1();\n    const configButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [dropdownPosition, setDropdownPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    });\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (configDropdownOpen === chat.id && configButtonRef.current) {\n            const rect = configButtonRef.current.getBoundingClientRect();\n            setDropdownPosition({\n                top: rect.top - 8,\n                left: rect.right - 180 // Alinhado à direita do botão, considerando largura do dropdown\n            });\n        }\n    }, [\n        configDropdownOpen,\n        chat.id\n    ]);\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(isActive ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\", \" \").concat(isAnimating ? \"animate-pulse bg-blue-500/20 scale-105\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left p-3 flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                        children: [\n                            chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-2 h-2 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 3,\n                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1201,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1200,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                strokeWidth: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"truncate text-sm font-semibold \".concat(isActive ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                    children: chat.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1214,\n                                    columnNumber: 15\n                                }, this),\n                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs flex-shrink-0 ml-2 \".concat(isActive ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                    children: formatTime(chat.lastMessageTime)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1222,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1.5 rounded-lg transition-all duration-200 backdrop-blur-sm \".concat(configDropdownOpen === chat.id ? \"bg-cyan-600/90 text-cyan-200\" : \"bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white\"),\n                                title: \"Configura\\xe7\\xf5es\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setConfigDropdownOpen(configDropdownOpen === chat.id ? null : chat.id);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3.5 h-3.5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1238,\n                                columnNumber: 11\n                            }, this),\n                            configDropdownOpen === chat.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-full right-0 mb-2 bg-blue-900/95 backdrop-blur-xl rounded-xl border border-blue-600/30 shadow-2xl shadow-blue-900/60 p-2 min-w-[180px] z-[9999] animate-in slide-in-from-bottom-2 duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onEdit(chat.id, chat.name);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Configura\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onStatisticsModal === null || onStatisticsModal === void 0 ? void 0 : onStatisticsModal(chat.id);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1285,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Estat\\xedsticas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onDownloadModal === null || onDownloadModal === void 0 ? void 0 : onDownloadModal(chat.id);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1258,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                        title: \"Deletar\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onDelete(chat.id, chat.name);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3.5 h-3.5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1319,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1177,\n        columnNumber: 5\n    }, this);\n}\n_s1(ChatItem, \"Di8QQJnlmJuzh/3cpcOqqXC5QpA=\");\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});